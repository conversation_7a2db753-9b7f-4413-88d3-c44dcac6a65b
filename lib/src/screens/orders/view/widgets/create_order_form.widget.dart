import 'dart:developer';

import 'package:dropx/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/shared/widgets/drop_downs/station_dropdown.dart';
import 'package:dropx/src/core/shared/widgets/drop_downs/type_dropdown.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/orders/models/type.model.dart';
import 'package:dropx/src/screens/orders/models/create_order_request.model.dart';
import 'package:dropx/src/screens/orders/providers/orders_providers.dart';
import 'package:dropx/src/core/shared/services/location_service.dart';
import 'package:xr_helper/xr_helper.dart';
import '../../../../core/consts/app_constants.dart';
import '../../models/pricing.model.dart';
import 'order_confirmation_dialog.widget.dart';

class CreateOrderFormWidget extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const CreateOrderFormWidget({
    super.key,
    required this.formKey,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final departureStation = useState<Station?>(null);
    final deliveryStation = useState<Station?>(null);
    final orderType = useState<OrderType?>(null);
    final isLoading = useState(false);
    final isFormValid = useState(false);

    // Form validation state
    final departureStationValid = useState(false);
    final deliveryStationValid = useState(false);
    final receiverNameValid = useState(false);
    final receiverPhoneValid = useState(false);
    final orderTypeValid = useState(false);

    // Pricing and distance state
    final currentPricing = useState<Pricing?>(null);
    final showDistance = useState(false);
    final travelTimeMinutes = useState<int?>(null);
    final isPricingLoading = useState(false);

    // Update form validity
    useEffect(() {
      isFormValid.value = departureStationValid.value &&
          deliveryStationValid.value &&
          receiverNameValid.value &&
          receiverPhoneValid.value &&
          orderTypeValid.value &&
          (departureStation.value?.id != deliveryStation.value?.id);
      return null;
    }, [
      departureStationValid.value,
      deliveryStationValid.value,
      receiverNameValid.value,
      receiverPhoneValid.value,
      orderTypeValid.value,
      departureStation.value,
      deliveryStation.value,
    ]);

    // Update station validation when stations change
    useEffect(() {
      departureStationValid.value = departureStation.value != null;
      deliveryStationValid.value = deliveryStation.value != null;
      return null;
    }, [departureStation.value, deliveryStation.value]);

    // Update type validation when type changes
    useEffect(() {
      orderTypeValid.value = orderType.value != null;
      return null;
    }, [orderType.value]);

    // Fetch pricing when stations and type are selected
    useEffect(() {
      if (departureStation.value != null &&
          deliveryStation.value != null &&
          orderType.value != null) {
        _fetchPricing(ref, departureStation.value!, deliveryStation.value!,
            orderType.value!, currentPricing, isPricingLoading);
      } else {
        currentPricing.value = null;
      }
      return null;
    }, [departureStation.value, deliveryStation.value, orderType.value]);

    void createOrder() {
      if (!formKey.currentState!.saveAndValidate()) return;

      if (!_validateStations(
          context, departureStation.value, deliveryStation.value)) {
        return;
      }

      final data = formKey.currentState?.instantValue ?? {};

      // validate type
      if (orderType.value == null) {
        showToast(context.tr.pleaseSelectOrderType, isError: true);
        return;
      }

      _handleCreateOrder(
        context,
        ref,
        departureStation,
        deliveryStation,
        orderType,
        data,
        isLoading,
        currentPricing.value,
        travelTimeMinutes.value,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Departure Station
          Text(
            context.tr.departureStation,
            style: AppTextStyles.labelLarge.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          AppGaps.gap8,
          StationDropdown(
            label: context.tr.departureStation,
            valueNotifier: departureStation,
          ),
          AppGaps.gap8,

          // Check Distance Button
          if (departureStation.value != null)
            _buildCheckDistanceButton(context, departureStation.value!,
                showDistance, travelTimeMinutes),

          AppGaps.gap16,

          // Delivery Station
          Text(
            context.tr.deliveryStation,
            style: AppTextStyles.labelLarge.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          AppGaps.gap8,
          StationDropdown(
            label: context.tr.deliveryStation,
            valueNotifier: deliveryStation,
          ),
          AppGaps.gap16,

          // Receiver Name
          BaseTextField(
            name: FieldsConsts.receiverName,
            title: context.tr.receiverName,
            textInputType: TextInputType.text,
            validator: (value) => Validations.mustBeNotEmpty(
              value,
              emptyMessage: context.tr.pleaseEnterReceiverName,
            ),
            realTimeValidator: (value) {
              final isValid = Validations.mustBeNotEmpty(value) == null;
              receiverNameValid.value = isValid;
              return null; // Don't show error in real-time for name
            },
          ),
          AppGaps.gap16,

          // Receiver Phone
          BaseTextField(
            name: FieldsConsts.receiverPhone,
            title: context.tr.receiverPhone,
            textInputType: TextInputType.phone,
            validator: (value) => Validations.palestinianPhoneNumber(
              value,
              emptyMessage: context.tr.pleaseEnterReceiverPhone,
              invalidMessage: context.tr.invalidPhoneFormat,
            ),
            realTimeValidator: (value) {
              final error = Validations.palestinianPhoneNumber(
                value,
                emptyMessage: context.tr.pleaseEnterReceiverPhone,
                invalidMessage: context.tr.invalidPhoneFormat,
              );
              receiverPhoneValid.value = error == null;
              return error;
            },
          ),
          AppGaps.gap16,

          // Order Type
          Text(
            context.tr.orderType,
            style: AppTextStyles.labelLarge.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          AppGaps.gap8,
          TypeDropdown(
            label: context.tr.orderType,
            valueNotifier: orderType,
          ),
          AppGaps.gap16,

          // Pricing Display
          if (currentPricing.value != null || isPricingLoading.value)
            _buildPricingDisplay(
                context, currentPricing.value, isPricingLoading.value),

          // Notes
          BaseTextField(
            name: FieldsConsts.notes,
            title: context.tr.notes,
            textInputType: TextInputType.multiline,
            maxLines: 3,
            isRequired: false,
          ),
          AppGaps.gap16,

          // Delivery Note
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '${context.tr.note}: ',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: Colors.red,
                      fontFamily: AppConsts.fontFamily,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextSpan(
                    text: context.tr.deliveryNote,
                    style: AppTextStyles.labelMedium.copyWith(
                      fontFamily: AppConsts.fontFamily,
                    ),
                  ),
                ],
              ),
            ),
          ),

          AppGaps.gap24,

          // Create Order Button
          Button(
            label: context.tr.confirmOrder,
            onPressed: createOrder,
            isLoading: isLoading.value,
            loadingWidget: LoadingWidget(),
            color: ColorManager.primaryColor,
          ),
        ],
      ),
    );
  }

  // Helper method to fetch pricing
  static Future<void> _fetchPricing(
    WidgetRef ref,
    Station departureStation,
    Station deliveryStation,
    OrderType orderType,
    ValueNotifier<Pricing?> currentPricing,
    ValueNotifier<bool> isPricingLoading,
  ) async {
    try {
      isPricingLoading.value = true;
      final pricing = await ref.read(getPricingFutureProvider({
        'fromStationId': departureStation.id,
        'toStationId': deliveryStation.id,
        'typeId': orderType.id,
      }).future);
      currentPricing.value = pricing;
    } catch (e) {
      Log.e('Error fetching pricing: $e');
      currentPricing.value = null;
    } finally {
      isPricingLoading.value = false;
    }
  }

  // Helper method to build pricing display
  Widget _buildPricingDisplay(
      BuildContext context, Pricing? pricing, bool isLoading) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: ColorManager.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ColorManager.primaryColor.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.attach_money,
            color: ColorManager.primaryColor,
            size: 24,
          ),
          AppGaps.gap12,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr.price,
                  style: AppTextStyles.labelMedium.copyWith(
                    color: ColorManager.greyText,
                  ),
                ),
                AppGaps.gap4,
                if (isLoading)
                  const SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else if (pricing != null)
                  Text(
                    '${pricing.price} ${context.tr.currency}',
                    style: AppTextStyles.title.copyWith(
                      color: ColorManager.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build check distance button
  Widget _buildCheckDistanceButton(
    BuildContext context,
    Station station,
    ValueNotifier<bool> showDistance,
    ValueNotifier<int?> travelTimeMinutes,
  ) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () async {
              if (showDistance.value) {
                showDistance.value = false;
                travelTimeMinutes.value = null;
              } else {
                await _calculateDistance(
                    station, showDistance, travelTimeMinutes);
              }
            },
            icon: Icon(
              showDistance.value ? Icons.visibility_off : Icons.location_on,
              size: 18,
            ),
            label: Text(
              showDistance.value
                  ? context.tr.hideDistance
                  : context.tr.checkDistance,
              style: AppTextStyles.labelMedium,
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: ColorManager.primaryColor,
              side: BorderSide(color: ColorManager.primaryColor),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            ),
          ),
        ),
        if (showDistance.value) ...[
          AppGaps.gap8,
          _buildDistanceDisplay(context, travelTimeMinutes.value),
        ],
      ],
    );
  }

  // Helper method to calculate distance
  static Future<void> _calculateDistance(
    Station station,
    ValueNotifier<bool> showDistance,
    ValueNotifier<int?> travelTimeMinutes,
  ) async {
    try {
      showDistance.value = true;
      final stationLat = double.tryParse(station.latitude);
      final stationLng = double.tryParse(station.longitude);

      if (stationLat != null && stationLng != null) {
        final travelTime =
            await LocationService.calculateTravelTimeFromCurrentLocation(
          destinationLat: stationLat,
          destinationLng: stationLng,
        );
        travelTimeMinutes.value = travelTime;
      }
    } catch (e) {
      Log.e('Error calculating distance: $e');
      travelTimeMinutes.value = null;
    }
  }

  // Helper method to build distance display
  Widget _buildDistanceDisplay(BuildContext context, int? travelTimeMinutes) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: Colors.blue,
            size: 18,
          ),
          AppGaps.gap8,
          Expanded(
            child: Text(
              travelTimeMinutes != null
                  ? '${context.tr.estimatedTravelTime}: $travelTimeMinutes ${context.tr.minutes}'
                  : context.tr.calculatingDistance,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.blue,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _validateStations(
      BuildContext context, Station? departure, Station? delivery) {
    if (departure == null) {
      showToast(context.tr.pleaseSelectDepartureStation, isError: true);
      return false;
    }

    if (delivery == null) {
      showToast(context.tr.pleaseSelectDeliveryStation, isError: true);
      return false;
    }

    if (departure.id == delivery.id) {
      showToast(context.tr.cannotSelectSameStation, isError: true);
      return false;
    }

    return true;
  }

  Future<void> _handleCreateOrder(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<Station?> departureStation,
    ValueNotifier<Station?> deliveryStation,
    ValueNotifier<OrderType?> orderType,
    Map<String, dynamic> formData,
    ValueNotifier<bool> isLoading,
    Pricing? existingPricing,
    int? existingTravelTime,
  ) async {
    try {
      isLoading.value = true;

      // Use existing pricing or fetch if not available
      Pricing? pricing = existingPricing;
      if (pricing == null) {
        pricing = await ref.read(getPricingFutureProvider({
          'fromStationId': departureStation.value!.id,
          'toStationId': deliveryStation.value!.id,
          'typeId': orderType.value!.id,
        }).future);
      }

      // Use existing travel time or calculate if not available
      int? travelTimeMinutes = existingTravelTime;
      if (travelTimeMinutes == null) {
        try {
          final departureStationLat =
              double.tryParse(departureStation.value!.latitude);
          final departureStationLng =
              double.tryParse(departureStation.value!.longitude);

          if (departureStationLat != null && departureStationLng != null) {
            travelTimeMinutes =
                await LocationService.calculateTravelTimeFromCurrentLocation(
              destinationLat: departureStationLat,
              destinationLng: departureStationLng,
            );
          }
        } catch (e) {
          Log.e('Error calculating travel time: $e');
          // Continue without travel time if calculation fails
        }
      }

      // Show confirmation dialog
      if (context.mounted) {
        final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => OrderConfirmationDialog(
            departureStation: departureStation.value!,
            deliveryStation: deliveryStation.value!,
            orderType: orderType.value!,
            receiverName: formData[FieldsConsts.receiverName] ?? '',
            receiverPhone: formData[FieldsConsts.receiverPhone] ?? '',
            notes: formData[FieldsConsts.notes] ?? '',
            pricing: pricing!,
            travelTimeMinutes: travelTimeMinutes,
          ),
        );

        if (confirmed == true) {
          // Create order
          final orderRequest = CreateOrderRequest(
            fromStationId: departureStation.value!.id,
            toStationId: deliveryStation.value!.id,
            typeId: orderType.value!.id,
            receiverName: formData[FieldsConsts.receiverName] ?? '',
            receiverPhone: formData[FieldsConsts.receiverPhone] ?? '',
            price: pricing!.price,
            note: formData[FieldsConsts.notes]?.isEmpty == true
                ? null
                : formData[FieldsConsts.notes],
          );

          final ordersController = ref.read(ordersControllerProvider);
          await ordersController.createOrder(orderRequest: orderRequest);

          // Show success message and navigate back
          if (context.mounted) {
            ref.invalidate(getOrdersFutureProvider);
            Navigator.of(context).pop();
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        showToast(context.tr.error, isError: true);
      }
    } finally {
      isLoading.value = false;
    }
  }
}
