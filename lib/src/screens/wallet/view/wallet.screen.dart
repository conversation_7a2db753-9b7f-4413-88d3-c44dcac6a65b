import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/wallet/models/wallet_info.model.dart';
import 'package:dropx/src/screens/wallet/models/wallet_transaction.model.dart';
import 'package:dropx/src/screens/wallet/providers/wallet_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class WalletScreen extends HookConsumerWidget {
  const WalletScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletController = ref.watch(walletControllerProvider);
    final walletInfo = useState<WalletInfoModel?>(null);
    final transactions = useState<List<WalletTransactionModel>>([]);
    final isLoading = useState(true);
    final isLoadingTransactions = useState(false);

    // Load wallet data on screen init
    useEffect(() {
      Future.microtask(() async {
        try {
          isLoading.value = true;
          final info = await walletController.getWalletInfo();
          walletInfo.value = info;

          final transactionsResponse =
              await walletController.getWalletTransactions();
          transactions.value = transactionsResponse.data;
        } catch (e) {
          // Handle error
          Log.e('Error loading wallet data: $e');
        } finally {
          isLoading.value = false;
        }
      });
      return null;
    }, []);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr.wallet,
          style: AppTextStyles.title,
        ),
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
      ),
      body: isLoading.value
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () async {
                try {
                  isLoadingTransactions.value = true;
                  final info = await walletController.getWalletInfo();
                  walletInfo.value = info;

                  final transactionsResponse =
                      await walletController.getWalletTransactions();
                  transactions.value = transactionsResponse.data;
                } catch (e) {
                  Log.e('Error refreshing wallet data: $e');
                } finally {
                  isLoadingTransactions.value = false;
                }
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Wallet Balance Section
                    _buildWalletBalanceCard(context, walletInfo.value),

                    AppGaps.gap24,

                    // Transactions Section
                    _buildTransactionsSection(context, transactions.value,
                        isLoadingTransactions.value),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildWalletBalanceCard(
      BuildContext context, WalletInfoModel? walletInfo) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            context.tr.currentPoints,
            style: AppTextStyles.subTitle,
          ),
          AppGaps.gap8,

          // Balance Display
          Text(
            '${walletInfo?.balance ?? 0}',
            style: AppTextStyles.headlineSmall.copyWith(
              fontSize: 32.sp,
              fontWeight: FontWeight.bold,
              color: ColorManager.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsSection(BuildContext context,
      List<WalletTransactionModel> transactions, bool isLoading) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Transactions Header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              context.tr.transactionsHistory,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            // Filter buttons could be added here
          ],
        ),

        AppGaps.gap16,

        // Transactions List
        if (isLoading)
          const Center(child: CircularProgressIndicator())
        else if (transactions.isEmpty)
          _buildEmptyTransactions(context)
        else
          ...transactions.map(
              (transaction) => _buildTransactionCard(context, transaction)),
      ],
    );
  }

  Widget _buildEmptyTransactions(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(32.w),
      child: Column(
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 64.w,
            color: ColorManager.greyIcon,
          ),
          AppGaps.gap16,
          Text(
            context.tr.noTransactionsYet,
            style: AppTextStyles.bodyMedium.copyWith(
              color: ColorManager.greyText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(
      BuildContext context, WalletTransactionModel transaction) {
    final DateTime? createdAt = DateTime.tryParse(transaction.createdAt);
    final String formattedDate = createdAt?.formatDateToString ?? '';
    final String formattedTime = createdAt != null
        ? '${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}'
        : '';

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Transaction Icon
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: transaction.type == 'earned'
                  ? ColorManager.primaryColor.withOpacity(0.1)
                  : ColorManager.errorColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              transaction.type == 'earned' ? Icons.add : Icons.remove,
              color: transaction.type == 'earned'
                  ? ColorManager.primaryColor
                  : ColorManager.errorColor,
              size: 20.w,
            ),
          ),

          AppGaps.gap12,

          // Transaction Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                AppGaps.gap4,
                Row(
                  children: [
                    Text(
                      formattedDate,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: ColorManager.greyText,
                      ),
                    ),
                    AppGaps.gap4,
                    Text(
                      formattedTime,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: ColorManager.greyText,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Points
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: ColorManager.lightGrey,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.close,
                  size: 16.w,
                  color: ColorManager.greyIcon,
                ),
                AppGaps.gap4,
                Text(
                  '${transaction.points}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: ColorManager.greyText,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
