import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/tabs/custom_tab_bar.widget.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/wallet/models/wallet_info.model.dart';
import 'package:dropx/src/screens/wallet/models/wallet_transaction.model.dart';
import 'package:dropx/src/screens/wallet/providers/wallet_providers.dart';
import 'package:dropx/src/screens/wallet/widgets/wallet_transaction_card.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class WalletScreen extends HookConsumerWidget {
  const WalletScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletController = ref.watch(walletControllerProvider);
    final walletInfo = useState<WalletInfoModel?>(null);
    final earnedTransactions = useState<List<WalletTransactionModel>>([]);
    final spentTransactions = useState<List<WalletTransactionModel>>([]);
    final isLoading = useState(true);
    final isLoadingEarned = useState(false);
    final isLoadingSpent = useState(false);

    // Load wallet data on screen init
    useEffect(() {
      Future.microtask(() async {
        try {
          isLoading.value = true;
          final info = await walletController.getWalletInfo();
          walletInfo.value = info;

          // Load both earned and spent transactions
          final earnedResponse =
              await walletController.getWalletTransactions(type: 'earned');
          earnedTransactions.value = earnedResponse.data;

          final spentResponse =
              await walletController.getWalletTransactions(type: 'spent');
          spentTransactions.value = spentResponse.data;
        } catch (e) {
          // Handle error
          Log.e('Error loading wallet data: $e');
        } finally {
          isLoading.value = false;
        }
      });
      return null;
    }, []);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr.wallet,
          style: AppTextStyles.title,
        ),
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
      ),
      body: isLoading.value
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () async {
                try {
                  isLoadingEarned.value = true;
                  isLoadingSpent.value = true;
                  final info = await walletController.getWalletInfo();
                  walletInfo.value = info;

                  final earnedResponse = await walletController
                      .getWalletTransactions(type: 'earned');
                  earnedTransactions.value = earnedResponse.data;

                  final spentResponse = await walletController
                      .getWalletTransactions(type: 'spent');
                  spentTransactions.value = spentResponse.data;
                } catch (e) {
                  Log.e('Error refreshing wallet data: $e');
                } finally {
                  isLoadingEarned.value = false;
                  isLoadingSpent.value = false;
                }
              },
              child: Column(
                children: [
                  // Wallet Balance Section (Fixed at top)
                  Padding(
                    padding: EdgeInsets.all(16.w),
                    child: _buildWalletBalanceCard(context, walletInfo.value),
                  ),

                  AppGaps.gap16,

                  // Transactions Tabs Section (Expandable)
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: CustomTabBarWidget(
                        tabTitles: [
                          context.tr.earnedPoints,
                          context.tr.spentPoints,
                        ],
                        children: [
                          // Earned Transactions Tab
                          _buildTransactionsList(
                            context,
                            earnedTransactions.value,
                            isLoadingEarned.value,
                            () async {
                              try {
                                isLoadingEarned.value = true;
                                final response = await walletController
                                    .getWalletTransactions(type: 'earned');
                                earnedTransactions.value = response.data;
                              } catch (e) {
                                Log.e('Error loading earned transactions: $e');
                              } finally {
                                isLoadingEarned.value = false;
                              }
                            },
                          ),
                          // Spent Transactions Tab
                          _buildTransactionsList(
                            context,
                            spentTransactions.value,
                            isLoadingSpent.value,
                            () async {
                              try {
                                isLoadingSpent.value = true;
                                final response = await walletController
                                    .getWalletTransactions(type: 'spent');
                                spentTransactions.value = response.data;
                              } catch (e) {
                                Log.e('Error loading spent transactions: $e');
                              } finally {
                                isLoadingSpent.value = false;
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildWalletBalanceCard(
      BuildContext context, WalletInfoModel? walletInfo) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            context.tr.currentPoints,
            style: AppTextStyles.subTitle,
          ),
          AppGaps.gap8,

          // Balance Display
          Text(
            '${walletInfo?.balance ?? 0}',
            style: AppTextStyles.headlineSmall.copyWith(
              fontSize: 32.sp,
              fontWeight: FontWeight.bold,
              color: ColorManager.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(
    BuildContext context,
    List<WalletTransactionModel> transactions,
    bool isLoading,
    VoidCallback onRefresh,
  ) {
    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      child: ListView(
        padding: EdgeInsets.all(16.w),
        children: [
          // Transactions List
          if (isLoading)
            const Center(child: CircularProgressIndicator())
          else if (transactions.isEmpty)
            _buildEmptyTransactions(context)
          else
            ...transactions.map((transaction) =>
                WalletTransactionCard(transaction: transaction)),
        ],
      ),
    );
  }

  Widget _buildEmptyTransactions(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(32.w),
      child: Column(
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 64.w,
            color: ColorManager.greyIcon,
          ),
          AppGaps.gap16,
          Text(
            context.tr.noTransactionsYet,
            style: AppTextStyles.bodyMedium.copyWith(
              color: ColorManager.greyText,
            ),
          ),
        ],
      ),
    );
  }
}
