import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/wallet/models/wallet_transaction.model.dart';
import 'package:xr_helper/xr_helper.dart';

class WalletTransactionCard extends StatelessWidget {
  final WalletTransactionModel transaction;

  const WalletTransactionCard({
    super.key,
    required this.transaction,
  });

  @override
  Widget build(BuildContext context) {
    final DateTime? createdAt = DateTime.tryParse(transaction.createdAt);
    final String formattedDate = createdAt?.formatDateToString ?? '';
    final String formattedTime = _formatTimeWithAmPm(createdAt);

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Transaction Icon
          _buildTransactionIcon(),

          AppGaps.gap12,

          // Transaction Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Transaction Description
                Text(
                  transaction.description,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),

                AppGaps.gap8,

                // Date and Time Row
                Row(
                  children: [
                    // Date Section
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.tr.today,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: ColorManager.greyText,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          AppGaps.gap4,
                          Text(
                            formattedDate,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: ColorManager.black,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),

                    AppGaps.gap16,

                    // Time Section
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.tr.time,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: ColorManager.greyText,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          AppGaps.gap4,
                          Text(
                            formattedTime,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: ColorManager.black,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          AppGaps.gap12,

          // Points Badge
          _buildPointsBadge(),
        ],
      ),
    );
  }

  Widget _buildTransactionIcon() {
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: transaction.type == 'earned'
            ? ColorManager.primaryColor.withOpacity(0.1)
            : ColorManager.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Icon(
        transaction.type == 'earned' ? Icons.add : Icons.remove,
        color: transaction.type == 'earned'
            ? ColorManager.primaryColor
            : ColorManager.errorColor,
        size: 20.w,
      ),
    );
  }

  Widget _buildPointsBadge() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: transaction.type == 'earned'
            ? ColorManager.primaryColor.withOpacity(0.1)
            : ColorManager.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: transaction.type == 'earned'
              ? ColorManager.primaryColor.withOpacity(0.3)
              : ColorManager.errorColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.close,
            size: 14.w,
            color: transaction.type == 'earned'
                ? ColorManager.primaryColor
                : ColorManager.errorColor,
          ),
          AppGaps.gap4,
          Text(
            '${transaction.points}',
            style: AppTextStyles.bodySmall.copyWith(
              color: transaction.type == 'earned'
                  ? ColorManager.primaryColor
                  : ColorManager.errorColor,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimeWithAmPm(DateTime? dateTime) {
    if (dateTime == null) return '';

    final hour = dateTime.hour;
    final minute = dateTime.minute;

    // Convert to 12-hour format
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final amPm = hour >= 12 ? 'PM' : 'AM';

    return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $amPm';
  }
}
