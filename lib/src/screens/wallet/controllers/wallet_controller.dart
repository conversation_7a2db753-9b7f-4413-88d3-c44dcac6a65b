import 'package:dropx/src/screens/wallet/models/wallet_info.model.dart';
import 'package:dropx/src/screens/wallet/models/wallet_transaction.model.dart';
import 'package:dropx/src/screens/wallet/repositories/wallet_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class WalletController extends BaseVM {
  final WalletRepository walletRepo;

  WalletController({
    required this.walletRepo,
  });

  // * Get Wallet Info
  Future<WalletInfoModel> getWalletInfo() async {
    return await baseFunction(
      () async {
        final walletInfo = await walletRepo.getWalletInfo();
        return walletInfo;
      },
    );
  }

  // * Get Wallet Transactions
  Future<WalletTransactionsResponse> getWalletTransactions({
    int page = 1,
  }) async {
    return await baseFunction(
      () async {
        final transactions = await walletRepo.getWalletTransactions(page: page);
        return transactions;
      },
    );
  }
}
