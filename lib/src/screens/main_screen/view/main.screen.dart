import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/home/<USER>/home.screen.dart';
import 'package:dropx/src/screens/orders/view/create_order.screen.dart';
import 'package:dropx/src/screens/orders/view/orders_history.screen.dart';
import 'package:dropx/src/screens/shipment_search/view/search_shipment.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../generated/assets.gen.dart';
import '../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';

class MainScreen extends HookConsumerWidget {
  final int? homeCurrentIndex;

  const MainScreen({
    super.key,
    this.homeCurrentIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final isExpanded = useState(false);
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 300),
    );

    return Scaffold(
      body: _SelectedScreen(
          currentIndex: currentIndex, homeCurrentIndex: homeCurrentIndex),
      bottomNavigationBar: const BottomNavBarWidget(),
      floatingActionButton: _AnimatedFloatingActionButton(
        isExpanded: isExpanded,
        animationController: animationController,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }
}

String selectedTitle(int currentIndex, BuildContext context) {
  switch (currentIndex) {
    case 0:
      return context.tr.homePage;
    case 1:
      return context.tr.ordersHistory;
    case 2:
      return context.tr.wallet;
    case 3:
      return context.tr.menu;
  }

  return context.tr.homePage;
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;
  final int? homeCurrentIndex;

  const _SelectedScreen({
    required this.currentIndex,
    this.homeCurrentIndex,
  });

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return HomeScreen(
          homeCurrentIndex: homeCurrentIndex,
        );
      case 1:
        return const OrdersHistoryScreen();
      case 2:
        return _buildComingSoonScreen(context, context.tr.wallet);
      case 3:
        return _buildComingSoonScreen(context, context.tr.menu);
    }
    return const SizedBox.shrink();
  }

  Widget _buildComingSoonScreen(BuildContext context, String title) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          title,
          style: AppTextStyles.title,
        ),
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 80.w,
              color: ColorManager.greyIcon,
            ),
            AppGaps.gap16,
            Text(
              context.tr.comingSoon,
              style: AppTextStyles.headlineSmall.copyWith(
                color: ColorManager.greyText,
              ),
            ),
            AppGaps.gap8,
            Text(
              context.tr.pageUnderDevelopment,
              style: AppTextStyles.bodyMedium.copyWith(
                color: ColorManager.greyText,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AnimatedFloatingActionButton extends HookWidget {
  final ValueNotifier<bool> isExpanded;
  final AnimationController animationController;

  const _AnimatedFloatingActionButton({
    required this.isExpanded,
    required this.animationController,
  });

  @override
  Widget build(BuildContext context) {
    final rotationAnimation = useAnimation(
      Tween<double>(begin: 0, end: 0.75).animate(
        CurvedAnimation(parent: animationController, curve: Curves.easeInOut),
      ),
    );

    final scaleAnimation = useAnimation(
      Tween<double>(begin: 0, end: 1).animate(
        CurvedAnimation(parent: animationController, curve: Curves.easeInOut),
      ),
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (isExpanded.value) ...[
          // Create Order Button
          Transform.scale(
            scale: scaleAnimation,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 16, left: 24, right: 24),
              child: FloatingActionButton.extended(
                heroTag: "create_order",
                backgroundColor: ColorManager.primaryColor,
                onPressed: () {
                  _toggleExpansion();
                  const CreateOrderScreen().navigate;
                },
                icon: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    context.tr.createOrder,
                    style: AppTextStyles.subTitle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                label: Assets.icons.createShipment.image(width: 25),
              ),
            ),
          ),
          // Search Order Button
          Transform.scale(
            scale: scaleAnimation,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 16, left: 24, right: 24),
              child: FloatingActionButton.extended(
                heroTag: "search_order",
                backgroundColor: ColorManager.orangeColor,
                onPressed: () {
                  _toggleExpansion();
                  const SearchShipmentScreen().navigate;
                },
                icon: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    context.tr.searchOrder,
                    style: AppTextStyles.subTitle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                label: Assets.icons.searchShipment.image(width: 25),
              ),
            ),
          ),
        ],
        // Main FAB
        Transform.rotate(
          angle: rotationAnimation * 3.14159,
          child: FloatingActionButton(
            onPressed: _toggleExpansion,
            backgroundColor: ColorManager.primaryColor,
            child: Icon(
              Icons.add,
              color: Colors.white,
              size: 30.w,
            ),
          ),
        ),

        if (isExpanded.value) AppGaps.gap48,
      ],
    );
  }

  void _toggleExpansion() {
    isExpanded.value = !isExpanded.value;
    if (isExpanded.value) {
      animationController.forward();
    } else {
      animationController.reverse();
    }
  }
}
